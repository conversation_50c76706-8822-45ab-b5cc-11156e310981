package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.exception.HealControlCenterException;
import com.heal.controlcenter.pojo.GroupKpiAttributeMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DAO class converted from appsone-controlcenter JDBI to JDBC.
 * Original appsone-controlcenter class used JDBI with Handle and BindInDao.
 * This implementation uses Spring JDBC with JdbcTemplate for database operations.
 */
@Slf4j
@Repository
public class BindInDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Gets group KPI attribute mapping - converted from appsone-controlcenter JDBI to JDBC.
     * 
     * Original appsone-controlcenter method signature:
     * public List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(int accountId, int instanceId, Handle handle) throws ControlCenterException {
     *     BindInDao dao = getDaoConnection(handle, BindInDao.class);
     *     try {
     *         return dao.getGroupKpiAttributeMapping(accountId, instanceId);
     *     } catch (Exception e) {
     *         LOGGER.error("Error while fetching group kpi attribute mapping details for instanceId [{}]", instanceId);
     *         throw new ControlCenterException("Error while fetching group kpi attribute mapping details for instanceId [{}]", String.valueOf(instanceId));
     *     } finally {
     *         closeDaoConnection(handle, dao);
     *     }
     * }
     * 
     * Original appsone-controlcenter JDBI query:
     * @SqlQuery("select attribute_value attributeValue, mst_kpi_details_id kpiId, alias_name aliasName from comp_instance_kpi_group_details where comp_instance_id = :compInstanceId")
     * 
     * @param accountId Account ID (kept for compatibility but not used in query)
     * @param instanceId Component instance ID
     * @return List of GroupKpiAttributeMapping matching original appsone-controlcenter behavior
     * @throws HealControlCenterException if error occurs during database operation
     */
    public List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(int accountId, int instanceId) throws HealControlCenterException {
        String sql = "SELECT attribute_value AS attributeValue, mst_kpi_details_id AS kpiId, alias_name AS aliasName " +
                     "FROM comp_instance_kpi_group_details WHERE comp_instance_id = ?";

        @SqlQuery("select attribute_value attributeValue, mst_kpi_details_id kpiId ,ifnull(alias_name, attribute_value) aliasName from comp_instance_kpi_group_details ckgd, comp_instance ci where ci.id=comp_instance_id and ci.account_id= :accountId and comp_instance_id = :instanceId")
        List<GroupKpiAttributeMapping> getGroupKpiAttributeMapping(@Bind("accountId") int accountId, @Bind("instanceId") int instanceId);
        
        try {
            log.debug("Fetching group kpi attribute mapping details for instanceId [{}]", instanceId);
            
            List<GroupKpiAttributeMapping> results = jdbcTemplate.query(sql, (rs, rowNum) -> {
                GroupKpiAttributeMapping mapping = new GroupKpiAttributeMapping();
                mapping.setAttributeValue(rs.getString("attributeValue"));
                mapping.setKpiId(rs.getInt("kpiId"));
                mapping.setAliasName(rs.getString("aliasName"));
                return mapping;
            }, instanceId);
            
            log.debug("Successfully fetched {} group kpi attribute mapping details for instanceId [{}]", results.size(), instanceId);
            return results;
            
        } catch (Exception e) {
            log.error("Error while fetching group kpi attribute mapping details for instanceId [{}]", instanceId, e);
            throw new HealControlCenterException("Error while fetching group kpi attribute mapping details for instanceId [" + instanceId + "]");
        }
    }
}
